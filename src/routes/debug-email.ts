import express from 'express';
import httpStatus from 'http-status';
import scheduleService from '../services/schedule.service';
import { scheduleLogger } from '../config/logger';

const auth = require('../middleware/auth');
const catchAsync = require('../helpers/catch-async');

const router = express.Router();

// Manual trigger for email digest
const triggerEmailDigest = catchAsync(async (req: any, res: any) => {
  const { frequency = 'daily' } = req.body;
  
  scheduleLogger.info(`Manual trigger: Email digest (${frequency}) started by ${req.user.email}`);
  
  try {
    await scheduleService.sendEmailDigest(frequency);
    scheduleLogger.info(`Manual trigger: Email digest (${frequency}) completed successfully`);
    
    res.status(httpStatus.OK).json({
      success: true,
      message: `Email digest (${frequency}) sent successfully`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    scheduleLogger.error(`Manual trigger: Email digest (${frequency}) failed:`, error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: `Email digest (${frequency}) failed`,
      error: error.message
    });
  }
});

// Manual trigger for upcoming report reminders
const triggerReportReminders = catchAsync(async (req: any, res: any) => {
  scheduleLogger.info(`Manual trigger: Report reminders started by ${req.user.email}`);
  
  try {
    await scheduleService.sendUpcomingReportReminders();
    scheduleLogger.info('Manual trigger: Report reminders completed successfully');
    
    res.status(httpStatus.OK).json({
      success: true,
      message: 'Report reminders sent successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    scheduleLogger.error('Manual trigger: Report reminders failed:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Report reminders failed',
      error: error.message
    });
  }
});

// Manual trigger for payment reminders
const triggerPaymentReminders = catchAsync(async (req: any, res: any) => {
  scheduleLogger.info(`Manual trigger: Payment reminders started by ${req.user.email}`);
  
  try {
    await scheduleService.sendPaymentReminderToProjectDirectors();
    scheduleLogger.info('Manual trigger: Payment reminders completed successfully');
    
    res.status(httpStatus.OK).json({
      success: true,
      message: 'Payment reminders sent successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    scheduleLogger.error('Manual trigger: Payment reminders failed:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Payment reminders failed',
      error: error.message
    });
  }
});

// Manual trigger for new grant opportunities
const triggerGrantOpportunities = catchAsync(async (req: any, res: any) => {
  scheduleLogger.info(`Manual trigger: Grant opportunities started by ${req.user.email}`);
  
  try {
    await scheduleService.sendNewGrantOpportunitiesNotification();
    scheduleLogger.info('Manual trigger: Grant opportunities completed successfully');
    
    res.status(httpStatus.OK).json({
      success: true,
      message: 'Grant opportunities notification sent successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    scheduleLogger.error('Manual trigger: Grant opportunities failed:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Grant opportunities notification failed',
      error: error.message
    });
  }
});

// Get debug information about scheduled jobs
const getScheduleStatus = catchAsync(async (req: any, res: any) => {
  try {
    const nextDueReports = await scheduleService.getNextDueReports();
    
    res.status(httpStatus.OK).json({
      success: true,
      data: {
        upcomingReports: {
          count: nextDueReports.length,
          reports: nextDueReports.slice(0, 5) // Show first 5 for debugging
        },
        schedules: {
          automatedNotification: '0 9 * * * (Daily at 9:00 AM)',
          paymentReminder: '0 9 * * * (Daily at 9:00 AM)',
          dailyDigest: '0 0 * * * (Daily at midnight)',
          weeklyDigest: '0 0 * * 0 (Sunday at midnight)',
          monthlyDigest: '0 0 1 * * (1st of month at midnight)',
          yearlyDigest: '0 0 1 1 * (January 1st at midnight)'
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    scheduleLogger.error('Get schedule status failed:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to get schedule status',
      error: error.message
    });
  }
});

// Routes (require admin authentication)
router.post('/digest', auth('admin'), triggerEmailDigest);
router.post('/reports', auth('admin'), triggerReportReminders);
router.post('/payments', auth('admin'), triggerPaymentReminders);
router.post('/grants', auth('admin'), triggerGrantOpportunities);
router.get('/status', auth('admin'), getScheduleStatus);

module.exports = router;
